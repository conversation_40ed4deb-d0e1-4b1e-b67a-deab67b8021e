<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="600"
    @cancel="cancel"
    modalHeight="500"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案名称：</label>
            <span class="common-value-text">{{ form.emergencyPlanName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案类型：</label>
            <span class="common-value-text">{{ emergencyPlanTypeFormat(form.emergencyPlanType) }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">批复时间：</label>
            <span class="common-value-text">{{ form.replyTime }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">负责人：</label>
            <span class="common-value-text">{{ form.chargeName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text" :title="form.projectName">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div style="margin-bottom: 20px">
            <label class="common-label-text">批文：</label>
            <div
              class="file-item"
              v-for="(el, i) in form.approvalAttaches"
              :key="i"
              @click="() => downLoad(el.attachUrl)"
            >
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24">
          <div>
            <label class="common-label-text">预案文件：</label>
            <div class="file-item" v-for="(el, i) in form.planAttaches" :key="i" @click="() => downLoad(el.attachUrl)">
              <a-icon type="paper-clip" />
              <div class="file-name" style="margin-left: 5px">
                {{ el.attachName }}
              </div>
            </div>
          </div>
        </a-col>

        <!-- 响应级别区块 -->
        <a-col :lg="24" :md="24" :sm="24" v-for="(level, index) in responseLevels" :key="level.key" class="response-level-block">
          <div class="response-level-container">
            <h3 class="response-level-title">{{ level.name }}</h3>
            
            <div class="response-content">
              <label class="response-label">响应内容：</label>
              <div class="response-content-text">{{ form.responseLevels[level.key].content }}</div>
            </div>
            
            <div class="response-config">
              <h4 class="config-title">响应配置：</h4>
              <div class="config-table">
                <div class="table-header">
                  <div class="table-cell header-cell">规则配置</div>
                  <div class="table-cell header-cell">报警内容</div>
                  <div class="table-cell header-cell">接收人</div>
                </div>
                
                <div 
                  class="table-row" 
                  v-for="(config, configIndex) in form.responseLevels[level.key].configs" 
                  :key="configIndex"
                >
                  <div class="table-cell">
                    <span class="rule-config-text">
                      {{ config.ruleConfig ? `阈值: ${config.ruleConfig.threshold} (${config.ruleConfig.operator})` : '未配置' }}
                    </span>
                  </div>
                  <div class="table-cell">
                    <span class="config-content-text">{{ config.alarmContent }}</span>
                  </div>
                  <div class="table-cell">
                    <span class="config-content-text">{{ config.receiver }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- <div class="form-item-title">配置模板内容</div> -->
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getPlanById } from '../services'
  import { getOptions } from '@/api/common'
  import AntModal from '@/components/pt/dialog/AntModal'
  import difference from 'lodash/difference'
  import * as _ from 'lodash'
  import moment from 'moment'

  export default {
    name: 'FormDetails',
    components: { AntModal },
    props: ['projectOptions', 'emergencyTypeOptions'],
    data() {
      return {
        modalLoading: false,
        formTitle: '',
        responseLevels: [
          { key: 'level1', name: 'I级响应' },
          { key: 'level2', name: 'II级响应' },
          { key: 'level3', name: 'III级响应' },
          { key: 'level4', name: 'IV级响应' },
        ],
        form: {
          approvalAttaches: [],
          chargeName: '',
          emergencyPlanId: null,
          emergencyPlanName: '',
          emergencyPlanType: null,
          planAttaches: [],
          projectId: null,
          replyTime: '',
          responseLevels: {
            level1: {
              content: 'I级响应内容示例：启动最高级别应急预案，全面动员相关部门和人员',
              configs: [
                { alarmContent: '水位超过警戒线10cm', receiver: '调度中心主任', ruleConfig: { threshold: 10, operator: '>' } },
                { alarmContent: '降雨量达到50mm/h', receiver: '值班工程师', ruleConfig: { threshold: 50, operator: '>=' } }
              ]
            },
            level2: {
              content: 'II级响应内容示例：启动重大级别应急预案，相关部门协调响应',
              configs: [
                { alarmContent: '水位超过警戒线5cm', receiver: '调度员', ruleConfig: { threshold: 5, operator: '>' } }
              ]
            },
            level3: {
              content: 'III级响应内容示例：启动较大级别应急预案，加强监测预警',
              configs: [
                { alarmContent: '水位接近警戒线', receiver: '监测人员', ruleConfig: { threshold: 0, operator: '>=' } }
              ]
            },
            level4: {
              content: 'IV级响应内容示例：启动一般级别应急预案，日常监测管理',
              configs: [
                { alarmContent: '水位正常范围内变化', receiver: '值班人员', ruleConfig: { threshold: -10, operator: '>' } }
              ]
            }
          }
        },
        open: false,
        rules: {},
      }
    },
    created() {},
    mounted() {},
    methods: {
      // 响应等级格式化
      emergencyPlanTypeFormat(value) {
        if (value) {
          return this.emergencyTypeOptions.find(item => item.key == value)?.value
        } else {
          return ''
        }
      },
      // 年份格式化
      yearFormat(value) {
        if (value) {
          return moment(value).format('YYYY')
        } else {
          return ''
        }
      },
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      // 表单重置
      reset() {
        if (this.$refs.form !== undefined) {
          this.$refs.form.resetFields()
          this.planDate = []
          this.taskDate = []
        }
      },

      downLoad(url) {
        window.open(url)
      },

      /** 新增按钮操作 */
      details(row) {
        this.open = true
        if (row != undefined) {
          this.formTitle = '查看'
          this.modalLoading = true
          getPlanById({ emergencyPlanId: row.emergencyPlanId }).then(res => {
            if (res.code == 200) {
              this.form = {
                ...res.data,
                responseLevels: {
                  level1: {
                    content: 'I级响应内容示例：启动最高级别应急预案，全面动员相关部门和人员',
                    configs: [
                      { alarmContent: '水位超过警戒线10cm', receiver: '调度中心主任', ruleConfig: { threshold: 10, operator: '>' } },
                      { alarmContent: '降雨量达到50mm/h', receiver: '值班工程师', ruleConfig: { threshold: 50, operator: '>=' } }
                    ]
                  },
                  level2: {
                    content: 'II级响应内容示例：启动重大级别应急预案，相关部门协调响应',
                    configs: [
                      { alarmContent: '水位超过警戒线5cm', receiver: '调度员', ruleConfig: { threshold: 5, operator: '>' } }
                    ]
                  },
                  level3: {
                    content: 'III级响应内容示例：启动较大级别应急预案，加强监测预警',
                    configs: [
                      { alarmContent: '水位接近警戒线', receiver: '监测人员', ruleConfig: { threshold: 0, operator: '>=' } }
                    ]
                  },
                  level4: {
                    content: 'IV级响应内容示例：启动一般级别应急预案，日常监测管理',
                    configs: [
                      { alarmContent: '水位正常范围内变化', receiver: '值班人员', ruleConfig: { threshold: -10, operator: '>' } }
                    ]
                  }
                }
              }
              this.form.emergencyPlanType = String(this.form.emergencyPlanType)
              this.modalLoading = false
              //附件显示
            }
          })
        }
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');

  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .file-item {
    display: flex;
    align-items: center;
    line-height: 25px;
  }

  .file-name {
    color: @primary-color;
    cursor: pointer;
    margin-right: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .response-level-block {
    margin-bottom: 24px;
    
    .response-level-container {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 16px;
      background-color: #fafafa;
    }
    
    .response-level-title {
      font-size: 16px;
      font-weight: 600;
      color: #111;
      margin-bottom: 16px;
      margin-top: 0;
    }
    
    .response-content {
      margin-bottom: 16px;
      
      .response-label {
        display: inline-block;
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      .response-content-text {
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px 12px;
        min-height: 40px;
        line-height: 1.5;
        color: #262626;
      }
    }
    
    .response-config {
      .config-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
        margin-top: 0;
      }
      
      .config-table {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background-color: #fff;
        
        .table-header {
          display: flex;
          background-color: #fafafa;
          border-bottom: 1px solid #d9d9d9;
          
          .header-cell {
            font-weight: 600;
            color: #262626;
          }
        }
        
        .table-row {
          display: flex;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
        }
        
        .table-cell {
          padding: 8px 12px;
          border-right: 1px solid #f0f0f0;
          display: flex;
          align-items: center;
          
          &:first-child {
            width: 180px;
          }
          
          &:nth-child(2),
          &:nth-child(3) {
            flex: 1;
          }
          
          &:last-child {
            border-right: none;
          }
          
          .rule-config-text {
            font-family: monospace;
            font-size: 12px;
            color: #666;
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
          }
          
          .config-content-text {
            color: #262626;
          }
        }
      }
    }
  }
</style>
