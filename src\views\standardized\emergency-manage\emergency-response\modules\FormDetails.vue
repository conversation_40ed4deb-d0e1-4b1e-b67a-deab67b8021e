<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="800"
    @cancel="cancel"
    modalHeight="600"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案名称：</label>
            <span class="common-value-text">{{ form.planName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">预案类型：</label>
            <span class="common-value-text">{{ form.planType }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">事件编码：</label>
            <span class="common-value-text">{{ form.eventCode }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">启动时间：</label>
            <span class="common-value-text">{{ form.startTime }}</span>
          </div>
        </a-col>

        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">所属工程：</label>
            <span class="common-value-text">{{ form.projectName }}</span>
          </div>
        </a-col>
        <a-col :lg="12" :md="12" :sm="24">
          <div class="item">
            <label class="common-label-text">负责人：</label>
            <span class="common-value-text">{{ form.chargeName }}</span>
          </div>
        </a-col>

        <!-- 响应等级区块 -->
        <a-col :lg="24" :md="24" :sm="24" class="response-level-block">
          <div class="response-level-container">
            <h3 class="response-level-title">{{ form.responseLevel }}级响应</h3>

            <div class="response-content">
              <label class="response-label">响应内容：</label>
              <div class="response-content-text">{{ form.responseContent }}</div>
            </div>

            <div class="response-condition">
              <label class="response-label">响应条件：</label>
              <div class="response-content-text">{{ form.responseCondition }}</div>
            </div>

            <div class="response-config">
              <h4 class="config-title">报警配置：</h4>
              <div class="config-table">
                <div class="table-header">
                  <div class="table-cell header-cell">报警内容</div>
                  <div class="table-cell header-cell">接收人</div>
                </div>

                <div
                  class="table-row"
                  v-for="(config, configIndex) in form.alarmConfigs"
                  :key="configIndex"
                >
                  <div class="table-cell">
                    <span class="config-content-text">{{ config.alarmContent }}</span>
                  </div>
                  <div class="table-cell">
                    <span class="config-content-text">{{ config.receiver }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'

export default {
  name: 'FormDetails',
  components: { AntModal },
  data() {
    return {
      modalLoading: false,
      formTitle: '详情',
      form: {
        planName: '',
        planType: '',
        eventCode: '',
        startTime: '',
        projectName: '',
        chargeName: '',
        responseLevel: '',
        responseContent: '',
        responseCondition: '',
        alarmConfigs: []
      },
      open: false,
      // 测试数据
      testData: [
        {
          id: 1,
          planName: '防洪应急预案',
          planType: '防洪预案',
          eventCode: 'ER2024001',
          startTime: '2024-01-15 08:30:00',
          projectName: '某某水库工程',
          chargeName: '张三',
          responseLevel: 'I',
          responseContent: 'I级响应：启动最高级别应急预案，全面动员相关部门和人员，实施紧急抢险救援措施，确保人员安全和工程安全。',
          responseCondition: '水位超过警戒线10cm，降雨量达到50mm/h',
          alarmConfigs: [
            { alarmContent: '水位超过警戒线10cm', receiver: '调度中心主任' },
            { alarmContent: '降雨量达到50mm/h', receiver: '值班工程师' },
            { alarmContent: '下游出现险情', receiver: '应急指挥部' }
          ]
        },
        {
          id: 2,
          planName: '抗旱应急预案',
          planType: '抗旱预案',
          eventCode: 'ER2024002',
          startTime: '2024-01-20 14:20:00',
          projectName: '某某灌区工程',
          chargeName: '李四',
          responseLevel: 'II',
          responseContent: 'II级响应：启动重大级别应急预案，相关部门协调响应，加强水源调度和用水管理。',
          responseCondition: '水位超过警戒线5cm',
          alarmConfigs: [
            { alarmContent: '水位超过警戒线5cm', receiver: '调度员' },
            { alarmContent: '供水压力不足', receiver: '运行值班员' }
          ]
        }
      ]
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false
      this.$emit('close')
    },

    /** 查看详情 */
    details(row) {
      this.open = true
      this.formTitle = '详情'
      this.modalLoading = true

      // 模拟API调用，使用测试数据
      setTimeout(() => {
        const testItem = this.testData.find(item => item.id === row.id)
        if (testItem) {
          this.form = { ...testItem }
        }
        this.modalLoading = false
      }, 300)
    },
  },
}
</script>

<style lang="less" scoped>
@import url('~@/global.less');

.item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.response-level-block {
  margin-bottom: 24px;

  .response-level-container {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;
  }

  .response-level-title {
    font-size: 16px;
    font-weight: 600;
    color: #111;
    margin-bottom: 16px;
    margin-top: 0;
  }

  .response-content, .response-condition {
    margin-bottom: 16px;

    .response-label {
      display: inline-block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .response-content-text {
      background-color: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px 12px;
      min-height: 40px;
      line-height: 1.5;
      color: #262626;
    }
  }

  .response-config {
    .config-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
      margin-top: 0;
    }

    .config-table {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background-color: #fff;

      .table-header {
        display: flex;
        background-color: #fafafa;
        border-bottom: 1px solid #d9d9d9;

        .header-cell {
          font-weight: 600;
          color: #262626;
        }
      }

      .table-row {
        display: flex;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }
      }

      .table-cell {
        padding: 8px 12px;
        border-right: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
        flex: 1;

        &:last-child {
          border-right: none;
        }

        .config-content-text {
          color: #262626;
        }
      }
    }
  }
}
</style>